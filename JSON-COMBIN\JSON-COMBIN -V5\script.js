document.addEventListener('DOMContentLoaded', () => {
    // Get DOM elements
    const fileInput = document.getElementById('json-files');
    const combineBtn = document.getElementById('combine-btn');
    const downloadBtn = document.getElementById('download-btn');
    const previewElement = document.getElementById('preview');
    const statsElement = document.getElementById('stats');
    const resultSection = document.getElementById('result-section');
    const splitFileInput = document.getElementById('json-file-to-split');
    const partsCountInput = document.getElementById('parts-count');
    const splitBtn = document.getElementById('split-btn');
    const splitDownloadContainer = document.getElementById('split-download-container');
    const combineTabBtn = document.getElementById('combine-tab-btn');
    const splitTabBtn = document.getElementById('split-tab-btn');
    const analyzeTabBtn = document.getElementById('analyze-tab-btn');
    const cardsTabBtn = document.getElementById('cards-tab-btn');
    const combineTab = document.getElementById('combine-tab');
    const splitTab = document.getElementById('split-tab');
    const analyzeTab = document.getElementById('analyze-tab');
    const cardsTab = document.getElementById('cards-tab');
    const analyzeFileInput = document.getElementById('json-file-to-analyze');
    const analyzeBtn = document.getElementById('analyze-btn');
    const cardsFileInput = document.getElementById('json-file-for-cards');
    const loadCardsBtn = document.getElementById('load-cards-btn');
    const cardsContainer = document.getElementById('cards-container');
    const downloadUpdatedBtn = document.getElementById('download-updated-btn');
    const cardsControls = document.getElementById('cards-controls');
    const searchInput = document.getElementById('search-input');
    const moviesCount = document.getElementById('movies-count');
    const editModal = document.getElementById('editModal');
    const editForm = document.getElementById('editForm');
    const editMovieName = document.getElementById('editMovieName');
    const editMovieImg = document.getElementById('editMovieImg');
    const editMovieHref = document.getElementById('editMovieHref');
    const cancelEditBtn = document.getElementById('cancelEdit');
    const closeModalBtn = document.querySelector('.close');

    // Store data
    let combinedData = null;
    let splitData = null;
    let splitParts = [];
    let analyzedData = null;
    let cardsData = null;
    let currentEditIndex = -1;
    let allMovies = []; // Store all movies for search functionality

    // Add event listeners
    combineBtn.addEventListener('click', combineJsonFiles);
    downloadBtn.addEventListener('click', downloadCombinedJson);
    splitBtn.addEventListener('click', splitJsonFile);
    analyzeBtn.addEventListener('click', analyzeJsonFile);
    loadCardsBtn.addEventListener('click', loadMovieCards);
    downloadUpdatedBtn.addEventListener('click', downloadUpdatedData);

    // Modal event listeners
    closeModalBtn.addEventListener('click', closeEditModal);
    cancelEditBtn.addEventListener('click', closeEditModal);
    editForm.addEventListener('submit', saveMovieEdit);

    // Close modal when clicking outside
    window.addEventListener('click', (event) => {
        if (event.target === editModal) {
            closeEditModal();
        }
    });

    // Search functionality
    searchInput.addEventListener('input', (event) => {
        const searchTerm = event.target.value.toLowerCase().trim();
        filterMovies(searchTerm);
    });

    // Tab switching
    combineTabBtn.addEventListener('click', () => {
        combineTabBtn.classList.add('active');
        splitTabBtn.classList.remove('active');
        analyzeTabBtn.classList.remove('active');
        cardsTabBtn.classList.remove('active');
        combineTab.style.display = 'block';
        splitTab.style.display = 'none';
        analyzeTab.style.display = 'none';
        cardsTab.style.display = 'none';

        // Reset UI for combine mode
        resetUI('combine');
    });

    splitTabBtn.addEventListener('click', () => {
        splitTabBtn.classList.add('active');
        combineTabBtn.classList.remove('active');
        analyzeTabBtn.classList.remove('active');
        cardsTabBtn.classList.remove('active');
        splitTab.style.display = 'block';
        combineTab.style.display = 'none';
        analyzeTab.style.display = 'none';
        cardsTab.style.display = 'none';

        // Reset UI for split mode
        resetUI('split');
    });

    analyzeTabBtn.addEventListener('click', () => {
        analyzeTabBtn.classList.add('active');
        combineTabBtn.classList.remove('active');
        splitTabBtn.classList.remove('active');
        cardsTabBtn.classList.remove('active');
        analyzeTab.style.display = 'block';
        combineTab.style.display = 'none';
        splitTab.style.display = 'none';
        cardsTab.style.display = 'none';

        // Reset UI for analyze mode
        resetUI('analyze');
    });

    cardsTabBtn.addEventListener('click', () => {
        cardsTabBtn.classList.add('active');
        combineTabBtn.classList.remove('active');
        splitTabBtn.classList.remove('active');
        analyzeTabBtn.classList.remove('active');
        cardsTab.style.display = 'block';
        combineTab.style.display = 'none';
        splitTab.style.display = 'none';
        analyzeTab.style.display = 'none';

        // Reset UI for cards mode
        resetUI('cards');
    });

    // Function to reset UI based on mode
    function resetUI(mode) {
        previewElement.textContent = '';
        statsElement.textContent = '';

        if (mode === 'combine') {
            downloadBtn.disabled = true;
            downloadBtn.style.display = 'block';
            splitDownloadContainer.style.display = 'none';
        } else if (mode === 'split') {
            downloadBtn.style.display = 'none';
            splitDownloadContainer.style.display = 'none';
            splitDownloadContainer.innerHTML = '';
        } else if (mode === 'analyze') {
            downloadBtn.style.display = 'none';
            splitDownloadContainer.style.display = 'none';
            splitDownloadContainer.innerHTML = '';
        } else if (mode === 'cards') {
            downloadBtn.style.display = 'none';
            splitDownloadContainer.style.display = 'none';
            splitDownloadContainer.innerHTML = '';
            cardsContainer.innerHTML = '';
            downloadUpdatedBtn.style.display = 'none';
            cardsControls.style.display = 'none';
            searchInput.value = '';
        }
    }

    // Function to combine JSON files
    async function combineJsonFiles() {
        const files = fileInput.files;

        // Check if files are selected
        if (files.length === 0) {
            showError('الرجاء اختيار ملف واحد على الأقل.');
            return;
        }

        try {
            // Reset UI
            previewElement.textContent = '';
            statsElement.textContent = '';
            downloadBtn.disabled = true;
            splitDownloadContainer.style.display = 'none';

            // Initialize combined data
            combinedData = {
                movies_info: []
            };

            // Process each file
            let totalMovies = 0;
            let processedFiles = 0;

            for (const file of files) {
                const fileContent = await readFile(file);

                try {
                    // Parse JSON
                    const jsonData = JSON.parse(fileContent);

                    // Validate structure
                    if (!jsonData.movies_info || !Array.isArray(jsonData.movies_info)) {
                        showError(`الملف ${file.name} لا يحتوي على بنية صحيحة. يجب أن يحتوي على مصفوفة "movies_info".`);
                        continue;
                    }

                    // Add movies to combined data
                    combinedData.movies_info = combinedData.movies_info.concat(jsonData.movies_info);

                    // Update stats
                    totalMovies += jsonData.movies_info.length;
                    processedFiles++;

                } catch (error) {
                    showError(`خطأ في معالجة الملف ${file.name}: ${error.message}`);
                }
            }

            // Display results
            if (processedFiles > 0) {
                statsElement.innerHTML = `
                    <p>تم معالجة <strong>${processedFiles}</strong> ملف.</p>
                    <p>إجمالي الأفلام: <strong>${totalMovies}</strong></p>
                `;

                // Show preview (limited to first 5 items)
                const previewData = {
                    movies_info: combinedData.movies_info.slice(0, 5)
                };

                if (combinedData.movies_info.length > 5) {
                    previewData._note = `عرض 5 من أصل ${combinedData.movies_info.length} فيلم`;
                }

                previewElement.textContent = JSON.stringify(previewData, null, 2);

                // Enable download button
                downloadBtn.disabled = false;
                downloadBtn.style.display = 'block';
            } else {
                showError('لم يتم معالجة أي ملفات بنجاح.');
            }

        } catch (error) {
            showError(`حدث خطأ: ${error.message}`);
        }
    }

    // Function to split JSON file
    async function splitJsonFile() {
        const file = splitFileInput.files[0];
        const partsCount = parseInt(partsCountInput.value);

        // Validate inputs
        if (!file) {
            showError('الرجاء اختيار ملف JSON للتجزئة.');
            return;
        }

        if (isNaN(partsCount) || partsCount < 1 || partsCount > 10) {
            showError('عدد الأجزاء يجب أن يكون بين 1 و 10.');
            return;
        }

        try {
            // Reset UI
            previewElement.textContent = '';
            statsElement.textContent = '';
            downloadBtn.style.display = 'none';
            splitDownloadContainer.style.display = 'none';
            splitDownloadContainer.innerHTML = '';

            // Read and parse the file
            const fileContent = await readFile(file);
            splitData = JSON.parse(fileContent);

            // Validate structure
            if (!splitData.movies_info || !Array.isArray(splitData.movies_info)) {
                showError(`الملف ${file.name} لا يحتوي على بنية صحيحة. يجب أن يحتوي على مصفوفة "movies_info".`);
                return;
            }

            const totalMovies = splitData.movies_info.length;

            // Check if splitting is needed
            if (partsCount === 1) {
                showError('تم اختيار جزء واحد فقط. لا حاجة للتجزئة.');
                return;
            }

            if (totalMovies < partsCount) {
                showError(`عدد الأفلام (${totalMovies}) أقل من عدد الأجزاء المطلوبة (${partsCount}). يرجى اختيار عدد أجزاء أقل.`);
                return;
            }

            // Calculate items per part
            const itemsPerPart = Math.ceil(totalMovies / partsCount);

            // Create split parts
            splitParts = [];

            for (let i = 0; i < partsCount; i++) {
                const startIndex = i * itemsPerPart;
                const endIndex = Math.min(startIndex + itemsPerPart, totalMovies);

                if (startIndex >= totalMovies) break;

                const partData = {
                    movies_info: splitData.movies_info.slice(startIndex, endIndex)
                };

                splitParts.push(partData);
            }

            // Display stats
            statsElement.innerHTML = `
                <p>تم تجزئة الملف <strong>${file.name}</strong> إلى <strong>${splitParts.length}</strong> أجزاء.</p>
                <p>إجمالي الأفلام: <strong>${totalMovies}</strong></p>
                <p>عدد الأفلام في كل جزء: <strong>${itemsPerPart}</strong> (تقريباً)</p>
            `;

            // Show preview of first part
            const previewData = {
                part: 1,
                total_parts: splitParts.length,
                movies_count: splitParts[0].movies_info.length,
                movies_info: splitParts[0].movies_info.slice(0, 5)
            };

            if (splitParts[0].movies_info.length > 5) {
                previewData._note = `عرض 5 من أصل ${splitParts[0].movies_info.length} فيلم في الجزء الأول`;
            }

            previewElement.textContent = JSON.stringify(previewData, null, 2);

            // Create download buttons for each part
            createSplitDownloadButtons(file.name, splitParts);

        } catch (error) {
            showError(`حدث خطأ: ${error.message}`);
        }
    }

    // Function to create download buttons for split parts
    function createSplitDownloadButtons(originalFileName, parts) {
        splitDownloadContainer.innerHTML = '';

        // Extract base name from original file
        let baseName = originalFileName.replace('.json', '');

        // Try to extract domain from the first movie's URL if available
        if (parts[0].movies_info.length > 0 && parts[0].movies_info[0].movies_href) {
            try {
                const url = new URL(parts[0].movies_info[0].movies_href);
                let domain = url.hostname.replace('www.', '');

                // If hostname has multiple parts, take the domain name part
                const domainParts = domain.split('.');
                if (domainParts.length >= 2) {
                    baseName = domainParts[domainParts.length - 2];
                } else {
                    baseName = domain;
                }
            } catch (e) {
                // If URL parsing fails, try to extract domain using regex
                const match = parts[0].movies_info[0].movies_href.match(/\/\/(?:www\.)?([^\/]+)/i);
                if (match && match[1]) {
                    baseName = match[1].split('.')[0];
                }
            }
        }

        // Create container title
        const containerTitle = document.createElement('h3');
        containerTitle.textContent = 'تحميل الأجزاء:';
        containerTitle.style.marginBottom = '10px';
        containerTitle.style.textAlign = 'center';
        splitDownloadContainer.appendChild(containerTitle);

        // Create buttons container
        const buttonsDiv = document.createElement('div');
        buttonsDiv.style.display = 'flex';
        buttonsDiv.style.flexWrap = 'wrap';
        buttonsDiv.style.justifyContent = 'center';
        buttonsDiv.style.gap = '10px';
        splitDownloadContainer.appendChild(buttonsDiv);

        // Create download buttons for each part
        parts.forEach((part, index) => {
            const partNumber = index + 1;
            const moviesCount = part.movies_info.length;
            const button = document.createElement('button');
            button.className = 'split-download-btn';
            button.textContent = `تحميل الجزء ${partNumber} (${moviesCount} فيلم)`;

            button.addEventListener('click', () => {
                // Create filename with base name, part number, and movies count
                const filename = `${baseName}_PART${partNumber}_of_${parts.length}_movies_${moviesCount}.json`;

                // Download the part
                downloadJsonData(part, filename);
            });

            buttonsDiv.appendChild(button);
        });

        // Show the download container
        splitDownloadContainer.style.display = 'block';
    }

    // Function to download JSON data
    function downloadJsonData(data, filename) {
        const jsonString = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();

        // Clean up
        setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }, 100);
    }

    // Function to read file content
    function readFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = (event) => {
                resolve(event.target.result);
            };

            reader.onerror = (error) => {
                reject(error);
            };

            reader.readAsText(file);
        });
    }

    // Function to download the combined JSON
    function downloadCombinedJson() {
        if (!combinedData) {
            showError('لا توجد بيانات للتحميل.');
            return;
        }

        // Extract main website name from movies_href
        let mainWebsite = 'movies';
        const totalMovies = combinedData.movies_info.length;

        // Try to extract domain from the first movie's URL
        if (totalMovies > 0 && combinedData.movies_info[0].movies_href) {
            try {
                const url = new URL(combinedData.movies_info[0].movies_href);
                mainWebsite = url.hostname.replace('www.', '');

                // If hostname has multiple parts, take the domain name part
                const parts = mainWebsite.split('.');
                if (parts.length >= 2) {
                    mainWebsite = parts[parts.length - 2];
                }
            } catch (e) {
                // If URL parsing fails, try to extract domain using regex
                const match = combinedData.movies_info[0].movies_href.match(/\/\/(?:www\.)?([^\/]+)/i);
                if (match && match[1]) {
                    mainWebsite = match[1].split('.')[0];
                }
            }
        }

        // Create filename with website name, COMBINED keyword, and total movies count
        const filename = `${mainWebsite}_COMBINED_movies_${totalMovies}.json`;

        // Download the combined data
        downloadJsonData(combinedData, filename);
    }

    // Function to analyze JSON file
    async function analyzeJsonFile() {
        const file = analyzeFileInput.files[0];

        // Validate input
        if (!file) {
            showError('الرجاء اختيار ملف JSON للتحليل.');
            return;
        }

        try {
            // Reset UI
            previewElement.textContent = '';
            statsElement.textContent = '';
            splitDownloadContainer.style.display = 'none';
            splitDownloadContainer.innerHTML = '';

            // Read and parse the file
            const fileContent = await readFile(file);
            analyzedData = JSON.parse(fileContent);

            // Detect structure: new (categories/movies) or old (movies_info)
            let totalMovies = 0;
            let statsHtml = `<p>تم تحليل الملف <strong>${file.name}</strong> بنجاح.</p>`;
            let previewData = {};

            if (analyzedData.movies && Array.isArray(analyzedData.movies) && analyzedData.categories && Array.isArray(analyzedData.categories)) {
                // New structure with categories and movies
                totalMovies = analyzedData.movies.length;
                statsHtml += `<p>إجمالي الأفلام: <strong>${totalMovies}</strong></p>`;

                // Count movies per category
                let categoryStats = '<ul style="margin-right:20px">';
                analyzedData.categories.forEach(cat => {
                    // Count movies in this category (by subCategories)
                    const count = analyzedData.movies.filter(mov => Array.isArray(mov.subCategories) && mov.subCategories.includes(cat.id)).length;
                    categoryStats += `<li><strong>${cat.name}</strong> (<span style='color:blue'>${count}</span>)</li>`;
                });
                categoryStats += '</ul>';
                statsHtml += `<p>عدد الأفلام في كل قسم:</p>${categoryStats}`;

                previewData = {
                    categories: analyzedData.categories,
                    movies: analyzedData.movies.slice(0, 5)
                };
                if (analyzedData.movies.length > 5) {
                    previewData._note = `عرض 5 من أصل ${analyzedData.movies.length} فيلم`;
                }
            } else if (analyzedData.movies_info && Array.isArray(analyzedData.movies_info)) {
                // Old structure
                totalMovies = analyzedData.movies_info.length;
                statsHtml += `<p>إجمالي الأفلام: <strong>${totalMovies}</strong></p>`;
                previewData = {
                    movies_info: analyzedData.movies_info.slice(0, 5)
                };
                if (analyzedData.movies_info.length > 5) {
                    previewData._note = `عرض 5 من أصل ${analyzedData.movies_info.length} فيلم`;
                }
            } else {
                showError(`الملف ${file.name} لا يحتوي على بنية صحيحة.`);
                return;
            }

            statsElement.innerHTML = statsHtml;
            previewElement.textContent = JSON.stringify(previewData, null, 2);

            // Create download button
            createAnalyzeDownloadButton(file.name, analyzedData);

        } catch (error) {
            showError(`حدث خطأ: ${error.message}`);
        }
    }

    // Function to create download button for analyzed file
    function createAnalyzeDownloadButton(originalFileName, data) {
        splitDownloadContainer.innerHTML = '';

        // Extract base name from original file
        let baseName = originalFileName.replace('.json', '');

        // Try to extract domain from the first movie's URL if available
        if (data.movies_info.length > 0 && data.movies_info[0].movies_href) {
            try {
                const url = new URL(data.movies_info[0].movies_href);
                let domain = url.hostname.replace('www.', '');

                // If hostname has multiple parts, take the domain name part
                const domainParts = domain.split('.');
                if (domainParts.length >= 2) {
                    baseName = domainParts[domainParts.length - 2];
                } else {
                    baseName = domain;
                }
            } catch (e) {
                // If URL parsing fails, try to extract domain using regex
                const match = data.movies_info[0].movies_href.match(/\/\/(?:www\.)?([^\/]+)/i);
                if (match && match[1]) {
                    baseName = match[1].split('.')[0];
                }
            }
        }

        // Create container title
        const containerTitle = document.createElement('h3');
        containerTitle.textContent = 'تحميل الملف:';
        containerTitle.style.marginBottom = '10px';
        containerTitle.style.textAlign = 'center';
        splitDownloadContainer.appendChild(containerTitle);

        // Create button
        const button = document.createElement('button');
        button.className = 'split-download-btn';
        const moviesCount = data.movies_info.length;
        button.textContent = `تحميل الملف (${moviesCount} فيلم)`;

        button.addEventListener('click', () => {
            // Create filename with base name and movies count
            const filename = `${baseName}_movies_${moviesCount}.json`;

            // Download the data
            downloadJsonData(data, filename);
        });

        splitDownloadContainer.appendChild(button);

        // Show the download container
        splitDownloadContainer.style.display = 'block';
    }

    // Function to load and display movie cards
    async function loadMovieCards() {
        const file = cardsFileInput.files[0];
        if (!file) {
            showError('الرجاء اختيار ملف JSON لعرض بطاقات الأفلام.');
            return;
        }
        try {
            // إظهار مؤشر التقدم
            const progressContainer = document.getElementById('progress-container');
            const progressText = document.getElementById('progress-text');
            const progressPercentage = document.getElementById('progress-percentage');
            const progressFill = document.getElementById('progress-fill');
            const progressDetails = document.getElementById('progress-details');
            progressContainer.style.display = 'block';
            progressText.textContent = 'جاري معالجة الأقسام...';
            progressPercentage.textContent = '0%';
            progressFill.style.width = '0%';
            progressDetails.textContent = '';

            // Read and parse the file
            const fileContent = await readFile(file);
            let json = {};
            try {
                json = JSON.parse(fileContent);
            } catch (e) {
                progressContainer.style.display = 'none';
                showError('ملف JSON غير صالح.');
                return;
            }

            // استخراج جميع الأفلام من جميع البنى الممكنة
            let moviesArr = [];
            function extractCardFields(obj) {
                return {
                    name: obj.movies_name || obj.series_name || obj.title || obj.name || '',
                    img: obj.movies_img || obj.series_img || obj.imageUrl || obj.img || '',
                    href: obj.movies_href || obj.series_href || obj.link || obj.href || '',
                    category: obj.category || obj.section || ''
                };
            }
            if (Array.isArray(json.movies_info)) {
                moviesArr = json.movies_info.map(extractCardFields);
            } else if (Array.isArray(json.movies)) {
                moviesArr = json.movies.map(extractCardFields);
            } else if (typeof json === 'object') {
                function extractMovies(obj) {
                    let arr = [];
                    for (const key in obj) {
                        if (Array.isArray(obj[key])) {
                            obj[key].forEach(item => {
                                if (typeof item === 'object') {
                                    if (item.movies_name || item.series_name || item.title || item.name) {
                                        arr.push(extractCardFields(item));
                                    } else {
                                        arr = arr.concat(extractMovies(item));
                                    }
                                }
                            });
                        } else if (typeof obj[key] === 'object') {
                            arr = arr.concat(extractMovies(obj[key]));
                        }
                    }
                    return arr;
                }
                moviesArr = extractMovies(json);
            }
            // تجاهل العناصر غير المكتملة (بدون اسم أو رابط)
            moviesArr = moviesArr.filter(m => m.name && m.href);

            // استخراج جميع الأقسام الفريدة من category
            const categoryMap = {};
            moviesArr.forEach(m => {
                let cat = m.category ? String(m.category).trim() : '';
                if (!cat) cat = 'غير مصنف';
                if (!categoryMap[cat]) categoryMap[cat] = [];
                categoryMap[cat].push(m);
            });
            const categories = Object.keys(categoryMap);

            // إذا كان لا يوجد إلا قسم واحد فقط واسمه "غير مصنف" أو كل العناصر بدون أقسام، اعرض البطاقات مباشرة
            if (categories.length === 1 && (categories[0] === 'غير مصنف' || categories[0] === '' || categories[0] === undefined)) {
                // عرض البطاقات مباشرة
                cardsContainer.innerHTML = '';
                window._currentDisplayedMovies = categoryMap[categories[0]];
                window._currentCategory = categories[0];
                // عرض البطاقات بدفعات سريعة
                let processed = 0;
                const total = window._currentDisplayedMovies.length;
                const batch = 40;
                function renderBatch(start) {
                    for (let i = start; i < Math.min(start + batch, total); i++) {
                        const card = createMovieCard(window._currentDisplayedMovies[i], i);
                        cardsContainer.appendChild(card);
                        processed++;
                    }
                    if (processed < total) {
                        setTimeout(() => renderBatch(processed), 0);
                    }
                }
                renderBatch(0);
                updateMoviesCount(window._currentDisplayedMovies.length, window._currentDisplayedMovies.length);
                progressText.textContent = 'تم عرض جميع البطاقات.';
                progressFill.style.width = '100%';
                progressPercentage.textContent = '100%';
                progressDetails.textContent = `تم العثور على ${window._currentDisplayedMovies.length} بطاقة.`;
                setTimeout(() => { progressContainer.style.display = 'none'; }, 700);
                cardsControls.style.display = 'block';
                downloadUpdatedBtn.style.display = 'none';
                document.querySelector('.container').classList.add('full-width');
                window._lastLoadedJson = json;
                return;
            }

            // عرض الأقسام كمجلدات
            cardsContainer.innerHTML = '';
            allMovies = [];
            window._categoryMoviesCache = categoryMap; // كاش للأفلام حسب القسم
            categories.forEach((cat, idx) => {
                const folder = document.createElement('div');
                folder.className = 'category-folder';
                folder.style.cssText = 'display:inline-block; margin:10px; padding:20px 30px; background:#f1f1f1; border-radius:8px; cursor:pointer; font-weight:bold; font-size:18px; box-shadow:0 2px 8px #eee; transition:background 0.2s;';
                folder.textContent = cat;
                folder.onclick = function() {
                    showCategoryCardsByCategory(cat, folder.textContent);
                };
                folder.onmouseover = function() { folder.style.background = '#e0ffe0'; };
                folder.onmouseout = function() { folder.style.background = '#f1f1f1'; };
                cardsContainer.appendChild(folder);
            });
            progressText.textContent = 'اختر قسماً لعرض البطاقات.';
            progressFill.style.width = '100%';
            progressPercentage.textContent = '100%';
            progressDetails.textContent = `تم العثور على ${categories.length} قسم`;
            setTimeout(() => { progressContainer.style.display = 'none'; }, 700);
            cardsControls.style.display = 'block';
            downloadUpdatedBtn.style.display = 'none';
            updateMoviesCount(0, 0);
            document.querySelector('.container').classList.add('full-width');
            window._lastLoadedJson = json;
        } catch (error) {
            document.getElementById('progress-container').style.display = 'none';
            showError(`حدث خطأ: ${error.message}`);
        }
    }

    // عرض بطاقات قسم معين حسب category
    function showCategoryCardsByCategory(cat, catName) {
        cardsContainer.innerHTML = '';
        const backBtn = document.createElement('button');
        backBtn.textContent = '◀ عودة للأقسام';
        backBtn.style.cssText = 'margin-bottom:20px; background:#007bff; color:#fff; font-size:16px; padding:8px 18px; border-radius:6px;';
        backBtn.onclick = () => loadMovieCards();
        cardsContainer.appendChild(backBtn);
        let moviesArr = (window._categoryMoviesCache && window._categoryMoviesCache[cat]) ? window._categoryMoviesCache[cat] : [];
        // إعادة استخراج الحقول المطلوبة من كل عنصر
        moviesArr = moviesArr.map(obj => ({
            name: obj.movies_name || obj.series_name || obj.title || obj.name || '',
            img: obj.movies_img || obj.series_img || obj.imageUrl || obj.img || '',
            href: obj.movies_href || obj.series_href || obj.link || obj.href || '',
            category: obj.category || obj.section || cat
        })).filter(m => m.name && m.href);
        if (!moviesArr || moviesArr.length === 0) {
            const msg = document.createElement('div');
            msg.textContent = `لا توجد أفلام في القسم: ${catName}`;
            msg.style.cssText = 'text-align:center; color:#666; margin-top:30px; font-size:18px;';
            cardsContainer.appendChild(msg);
            updateMoviesCount(0, 0);
            return;
        }
        // حفظ مصفوفة البطاقات المعروضة حالياً في متغير عام
        window._currentDisplayedMovies = moviesArr;
        window._currentCategory = cat;
        // عرض البطاقات بدفعات سريعة
        let processed = 0;
        const total = moviesArr.length;
        const batch = 40;
        function renderBatch(start) {
            for (let i = start; i < Math.min(start + batch, total); i++) {
                const card = createMovieCard(moviesArr[i], i);
                cardsContainer.appendChild(card);
                processed++;
            }
            if (processed < total) {
                setTimeout(() => renderBatch(processed), 0);
            }
        }
        renderBatch(0);
        updateMoviesCount(moviesArr.length, moviesArr.length);
    }

    // Function to create a movie card
    function createMovieCard(movie, index) {
        const card = document.createElement('div');
        card.className = 'movie-card';

        card.innerHTML = `
            <img class="movie-poster" src="${movie.img}" alt="${movie.name}"
                 onerror="this.className='movie-poster error'; this.innerHTML='صورة غير متاحة';">
            <div class="movie-info">
                <h3 class="movie-title">${movie.name}</h3>
                <div class="movie-actions">
                    <a href="${movie.href}" target="_blank" class="action-btn play-btn">
                        ▶ تشغيل
                    </a>
                    <button class="action-btn edit-btn" onclick="editMovie(${index})">
                        ✏ تعديل
                    </button>
                    <button class="action-btn delete-btn" onclick="deleteMovie(${index})">
                        🗑 حذف
                    </button>
                </div>
            </div>
        `;

        return card;
    }

    // Function to edit movie (تعمل على البطاقات المعروضة حالياً)
    window.editMovie = function(index) {
        if (!window._currentDisplayedMovies || !window._currentDisplayedMovies[index]) return;
        const movie = window._currentDisplayedMovies[index];
        window._currentEditIndex = index;
        // Fill the form with current data
        editMovieName.value = movie.name;
        editMovieImg.value = movie.img;
        editMovieHref.value = movie.href;
        // Show the modal
        editModal.style.display = 'block';
    };

    // Function to delete movie (تعمل على البطاقات المعروضة حالياً)
    window.deleteMovie = function(index) {
        if (!window._currentDisplayedMovies || !window._currentDisplayedMovies[index]) return;
        const movie = window._currentDisplayedMovies[index];
        if (confirm(`هل أنت متأكد من حذف الفيلم "${movie.name}"؟`)) {
            // Remove the movie from the array
            window._currentDisplayedMovies.splice(index, 1);
            // تحديث الكاش الخاص بالقسم
            if (window._categoryMoviesCache && window._currentCategory) {
                window._categoryMoviesCache[window._currentCategory] = window._currentDisplayedMovies;
            }
            // إعادة عرض القسم الحالي
            showCategoryCardsByCategory(window._currentCategory, window._currentCategory);
            showSuccess(`تم حذف الفيلم "${movie.name}" بنجاح.`);
        }
    };

    // Function to close edit modal
    function closeEditModal() {
        editModal.style.display = 'none';
        window._currentEditIndex = -1;
        editForm.reset();
    }

    // Function to save movie edit (تعمل على البطاقات المعروضة حالياً)
    function saveMovieEdit(event) {
        event.preventDefault();
        if (window._currentEditIndex === -1 || !window._currentDisplayedMovies || !window._currentDisplayedMovies[window._currentEditIndex]) {
            showError('خطأ في تحديد الفيلم للتعديل.');
            return;
        }
        // Update the movie data
        window._currentDisplayedMovies[window._currentEditIndex] = {
            name: editMovieName.value.trim(),
            img: editMovieImg.value.trim(),
            href: editMovieHref.value.trim(),
            category: window._currentDisplayedMovies[window._currentEditIndex].category || window._currentCategory
        };
        // تحديث الكاش الخاص بالقسم
        if (window._categoryMoviesCache && window._currentCategory) {
            window._categoryMoviesCache[window._currentCategory] = window._currentDisplayedMovies;
        }
        // إعادة عرض القسم الحالي
        showCategoryCardsByCategory(window._currentCategory, window._currentCategory);
        closeEditModal();
        showSuccess('تم تحديث بيانات الفيلم بنجاح.');
    }

    // Function to show success message
    function showSuccess(message) {
        const successElement = document.createElement('div');
        successElement.className = 'success';
        successElement.style.cssText = `
            color: #155724;
            background-color: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            border: 1px solid #c3e6cb;
        `;
        successElement.textContent = message;

        // Remove any existing success messages
        const existingSuccess = cardsTab.querySelectorAll('.success');
        existingSuccess.forEach(el => el.remove());

        // Add new success message
        cardsTab.insertBefore(successElement, cardsContainer);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            successElement.remove();
        }, 3000);
    }

    // Function to download updated data
    function downloadUpdatedData() {
        if (!cardsData || !cardsData.movies_info) {
            showError('لا توجد بيانات للتحميل.');
            return;
        }

        // Create filename with timestamp
        const now = new Date();
        const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '');
        const filename = `movies_updated_${timestamp}.json`;

        // Download the updated data
        downloadJsonData(cardsData, filename);

        showSuccess('تم تحميل البيانات المحدثة بنجاح.');
    }

    // Function to filter movies based on search term
    function filterMovies(searchTerm) {
        if (!allMovies || allMovies.length === 0) return;

        let filteredMovies;

        if (searchTerm === '') {
            // Show all movies if search is empty
            filteredMovies = allMovies;
        } else {
            // Filter movies based on search term
            filteredMovies = allMovies.filter(movie =>
                movie.movies_name.toLowerCase().includes(searchTerm)
            );
        }

        // Display filtered movies
        displayMovieCards(filteredMovies);

        // Update movies count
        updateMoviesCount(filteredMovies.length, allMovies.length);
    }

    // Function to update movies count display
    function updateMoviesCount(displayed, total = null) {
        if (total === null) {
            total = displayed;
        }

        if (displayed === total) {
            moviesCount.textContent = `إجمالي الأفلام: ${total}`;
        } else {
            moviesCount.textContent = `عرض ${displayed} من أصل ${total} فيلم`;
        }
    }

    // Function to show error message
    function showError(message) {
        const errorElement = document.createElement('div');
        errorElement.className = 'error';
        errorElement.textContent = message;

        // Remove any existing error messages
        const existingErrors = resultSection.querySelectorAll('.error');
        existingErrors.forEach(el => el.remove());

        // Add new error message
        resultSection.insertBefore(errorElement, resultSection.firstChild);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            errorElement.remove();
        }, 5000);
    }
});
