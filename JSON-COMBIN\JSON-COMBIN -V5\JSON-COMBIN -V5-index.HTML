<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title> JSON FILE EDITOR v5</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>JSON FILE EDITOR v5</h1>

        <div class="tabs">
            <button class="tab-btn active" id="combine-tab-btn">دمج الملفات</button>
            <button class="tab-btn" id="split-tab-btn">تجزئة الملفات</button>
            <button class="tab-btn" id="analyze-tab-btn">تحليل الملف</button>
            <button class="tab-btn" id="cards-tab-btn">عرض بطاقات الأفلام</button>
        </div>

        <div class="tab-content" id="combine-tab">
            <div class="upload-section">
                <label for="json-files">اختر ملفات JSON للدمج:</label>
                <input type="file" id="json-files" accept=".json" multiple>
                <button id="combine-btn">دمج الملفات</button>
            </div>
        </div>

        <div class="tab-content" id="split-tab" style="display: none;">
            <div class="upload-section">
                <label for="json-file-to-split">اختر ملف JSON للتجزئة:</label>
                <input type="file" id="json-file-to-split" accept=".json">
                <div class="parts-selector">
                    <label for="parts-count">عدد الأجزاء (1-10):</label>
                    <input type="number" id="parts-count" min="1" max="10" value="2">
                </div>
                <button id="split-btn">تجزئة الملف</button>
            </div>
        </div>

        <div class="tab-content" id="analyze-tab" style="display: none;">
            <div class="upload-section">
                <label for="json-file-to-analyze">اختر ملف JSON للتحليل:</label>
                <input type="file" id="json-file-to-analyze" accept=".json">
                <button id="analyze-btn">تحليل الملف</button>
            </div>
        </div>

        <div class="tab-content" id="cards-tab" style="display: none;">
            <div class="upload-section">
                <label for="json-file-for-cards">اختر ملف JSON لعرض بطاقات الأفلام:</label>
                <input type="file" id="json-file-for-cards" accept=".json">
                <button id="load-cards-btn">تحميل البطاقات</button>
                <button id="download-updated-btn" style="display: none; margin-right: 10px;">تحميل البيانات المحدثة</button>
            </div>

            <!-- Progress Bar -->
            <div class="progress-container" id="progress-container" style="display: none;">
                <div class="progress-info">
                    <span id="progress-text">جاري معالجة البيانات...</span>
                    <span id="progress-percentage">0%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div class="progress-details" id="progress-details">
                    تم معالجة 0 من 0 فيلم
                </div>
            </div>

            <div class="cards-controls" id="cards-controls" style="display: none;">
                <div class="search-section">
                    <input type="text" id="search-input" placeholder="البحث في الأفلام..." style="width: 300px; padding: 8px; margin-left: 10px;">
                    <span id="movies-count" class="movies-count"></span>
                </div>
            </div>

            <div class="cards-container" id="cards-container">
                <!-- Movie cards will be displayed here -->
            </div>
        </div>

        <div class="result-section" id="result-section">
            <h2>النتيجة</h2>
            <div class="stats" id="stats"></div>
            <pre id="preview"></pre>
            <div class="download-buttons">
                <button id="download-btn" disabled>تحميل الملف المدمج</button>
                <div id="split-download-container" style="display: none;"></div>
            </div>
        </div>
    </div>

    <!-- Edit Movie Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>تعديل بيانات الفيلم</h3>
            <form id="editForm">
                <label for="editMovieName">اسم الفيلم:</label>
                <input type="text" id="editMovieName" required>

                <label for="editMovieImg">رابط صورة الفيلم:</label>
                <input type="url" id="editMovieImg" required>

                <label for="editMovieHref">رابط الفيلم:</label>
                <input type="url" id="editMovieHref" required>

                <div class="modal-actions">
                    <button type="button" id="cancelEdit">إلغاء</button>
                    <button type="submit" id="saveEdit">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
